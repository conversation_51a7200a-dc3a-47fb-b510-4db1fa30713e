micronaut:
  server:
    port: 8081
  security:
    token:
      jwt:
        signatures:
          secret:
            generator:
              secret: pleaseChangeThisSecretForANewOne
  http:
    services:
      file-service:
        urls:
          - "http://localhost:8085"
endpoints:
  routes:
    sensitive: false

jpa:
  default:
    entity-scan:
      packages: 'cz.deuss.companyservice.database.model'
    properties:
      hibernate:
        show_sql: true
        format_sql: true
        use_sql_comments: true

deuss:
  blockchain:
    url: "http://127.0.0.1:8545"
    contracts:
      companyWalletRegistry: "******************************************"
      bondRegistryV2: "******************************************"
      adminPrivateKey: "0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d"