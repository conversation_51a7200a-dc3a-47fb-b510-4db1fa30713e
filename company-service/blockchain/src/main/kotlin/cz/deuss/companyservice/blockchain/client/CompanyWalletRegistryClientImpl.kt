package cz.deuss.companyservice.blockchain.client

import cz.deuss.companyservice.blockchain.config.BlockchainConfiguration
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import deuss.contract.company.CompanyWalletRegistry
import io.ethers.core.types.Address
import io.ethers.providers.middleware.Middleware
import io.ethers.signers.PrivateKeySigner
import jakarta.inject.Singleton

import java.util.*

/**
 * Implementation of CompanyWalletClient that interacts with the CompanyWalletRegistry smart contract.
 */
@Singleton
class CompanyWalletRegistryClientImpl(
    private val middleware: Middleware,
    private val blockchainConfiguration: BlockchainConfiguration,
) : CompanyWalletRegistryClient {

    private val logger = DeussLogger.semanticLogger(CompanyWalletRegistryClientImpl::class)

    private val adminSigner: PrivateKeySigner by lazy {
        PrivateKeySigner(blockchainConfiguration.adminPrivateKey)
    }

    private val companyWalletRegistry: CompanyWalletRegistry by lazy {
        CompanyWalletRegistry(middleware, blockchainConfiguration.companyWalletRegistryAddress)
    }

    init {
        createCompanyWallet(UUID.randomUUID(), Address("******************************************"))
        //enableCompanyWallet(Address("******************************************"))
    }

    override fun createCompanyWallet(companyId: UUID, ownerAddress: Address): Address {
        logger.message("Creating company wallet for companyId: $companyId").info()

        try {

            logger.message("Registering company wallet with owner: $ownerAddress").debug()

            // Execute the blockchain transaction
            val functionCall = companyWalletRegistry.registerCompanyWallet(ownerAddress)
            val pendingTransaction = functionCall.send(adminSigner).sendAwait().unwrap()

            // Wait for the transaction to be mined and get the receipt
            val receipt = pendingTransaction.awaitInclusion().unwrap()

            // Fetch logs using the transaction's hash
            val logs = receipt.logs

            // Filter for the specific CompanyWalletCreated event
            val events = logs.mapNotNull { CompanyWalletRegistry.CompanyWalletRegistered.decode(it) }


            if (events.isNotEmpty()) {
                val companyWalletAddress = events.first().companyWallet
                println("Company wallet created at: $companyWalletAddress")
                logger.message("Successfully created company wallet at address: $companyWalletAddress for companyId: $companyId").info()
                return companyWalletAddress
            } else {
                throw CompanyWalletCreationException("Failed to create company wallet for companyId: $companyId, EVENT NOT FOUND")
            }

            // The return value should be extracted from the transaction receipt
            // For now, return the derived owner address as the contract creates a wallet for this owner

        } catch (e: Exception) {
            logger.message("Failed to create company wallet for companyId: $companyId")
                .error()
            throw CompanyWalletCreationException("Failed to create company wallet for companyId: $companyId", e)
        }
    }


}
