package cz.deuss.companyservice.blockchain.config

import io.ethers.core.types.Address
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties

@ConfigurationProperties("deuss.blockchain.contracts")
data class BlockchainConfiguration @ConfigurationInject constructor(
    val companyWalletRegistry: String,
    val companyWalletFactory: String,
    val bondRegistryV2: String,
    val adminPrivateKey: String,
) {
    val companyWalletRegistryAddress: Address
        get() = Address(companyWalletRegistry)

    val companyWalletFactoryAddress: Address
        get() = Address(companyWalletFactory)

    val bondRegistryV2Address: Address
        get() = Address(bondRegistryV2)
}
