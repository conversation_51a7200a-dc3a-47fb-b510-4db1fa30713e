package cz.deuss.companyservice.blockchain.client

import io.ethers.core.types.Address
import java.util.*

/**
 * Client interface for company wallet blockchain operations. Handles creation and management of
 * company wallets on the blockchain.
 */
interface CompanyWalletRegistryClient {

    /**
     * Creates a new company wallet on the blockchain.
     * @param companyId The UUID of the company that needs a wallet
     * @param ownerAddress The blockchain address of the wallet owner
     * @return The blockchain address of the created and enabled wallet
     * @throws CompanyWalletCreationException if wallet creation fails
     */
    fun createCompanyWallet(companyId: UUID, ownerAddress: Address): Address

}

/**
 * Exception thrown when company wallet creation fails on the blockchain.
 */
class CompanyWalletCreationException(message: String, cause: Throwable? = null) : Exception(message, cause)
